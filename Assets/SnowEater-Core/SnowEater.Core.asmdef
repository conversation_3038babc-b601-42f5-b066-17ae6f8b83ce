{"name": "SnowEater.Core", "rootNamespace": "", "references": ["GUID:d45d63a8668f94083bb44cbb69873b95", "GUID:eec0964c48f6f4e40bc3ec2257ccf8c5", "GUID:84651a3751eca9349aac36a66bba901b"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}