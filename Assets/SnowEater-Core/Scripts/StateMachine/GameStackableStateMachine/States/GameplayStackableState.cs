
using SnowEater.Core.Events;
using SnowEater.Core.StateMachine.Stackable;
using SnowEater.HelloWorld.UI;
using UnityEngine;

namespace SnowEater.Core.StateMachine.GameState
{
    public class GameplayStackableState : GameStackableState
    {

        public GameplayStackableState(GameStackableStateContext gameStateContext, GameStackableStateEnum stateKey) : base(gameStateContext, stateKey)
        {
        }

        public override void EnterState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            base.EnterState(stateManager);
            _windowManagerService.PushWindow<PlayerHUD>();
            Cursor.lockState = CursorLockMode.Locked;
            Time.timeScale = 1f;

        }
        public override void ExitState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            Debug.Log($"Exited: {GetType().Name}");
        }

        public override CoreState<GameStackableStateEnum> UpdateState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            // Debug.Log($"Currently Running: {GetType().Name}");
            return this;
        }

        public override void ReturnFromSubState(CoreStateMachine<GameStackableStateEnum> stateManager, CoreState<GameStackableStateEnum> previousState)
        {
            base.ReturnFromSubState(stateManager, previousState);
            Debug.Log($"Returning From: {previousState.GetType().Name}");
            Cursor.lockState = CursorLockMode.Locked;
            Time.timeScale = 1f;
        }

        // Event Handling
    }
}