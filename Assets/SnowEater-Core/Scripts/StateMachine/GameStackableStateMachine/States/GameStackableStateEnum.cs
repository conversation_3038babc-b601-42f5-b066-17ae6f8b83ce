// Copyright Snow Eater Studios

using SnowEater.Core.Utils.ExpandableEnum;

namespace SnowEater.Core.StateMachine.GameState
{
    public class GameStackableStateEnum : StringEnum<GameStackableStateEnum>
    {
        public static readonly GameStackableStateEnum TITLE_STATE = new GameStackableStateEnum("TitleState", nameof(TITLE_STATE));
        public static readonly GameStackableStateEnum GAMEPLAY_STATE = new GameStackableStateEnum("GameplayState", nameof(GAMEPLAY_STATE));
        public static readonly GameStackableStateEnum PAUSE_STATE = new GameStackableStateEnum("PauseState", nameof(PAUSE_STATE));
        protected internal GameStackableStateEnum(string value, string name) : base(value, name)
        {

        }
    }
}