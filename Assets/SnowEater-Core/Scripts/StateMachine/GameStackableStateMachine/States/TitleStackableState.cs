
using SnowEater.Core.StateMachine.Stackable;
using SnowEater.Core.UI;
using UnityEngine;

namespace SnowEater.Core.StateMachine.GameState
{
    public class TitleStackableState : GameStackableState
    {

        public TitleStackableState(GameStackableStateContext gameStateContext, GameStackableStateEnum stateKey) : base(gameStateContext, stateKey)
        {
        }

        public override void EnterState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            base.EnterState(stateManager);
            _windowManagerService.PushWindow<TitleScreen>();
            Cursor.lockState = CursorLockMode.None;
            Time.timeScale = 0f;
        }

        public override void ExitState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            base.ExitState(stateManager);
            Debug.Log($"Exited: {GetType().Name}");

            _windowManagerService.PopWindow("TitleScreenLayer");
        }

        public override CoreState<GameStackableStateEnum> UpdateState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            base.UpdateState(stateManager);
            return this;
        }
    }
}