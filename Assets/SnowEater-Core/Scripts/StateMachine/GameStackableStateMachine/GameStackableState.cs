

using SnowEater.Core.Events;
using SnowEater.Core.Services;
using SnowEater.Core.StateMachine.Stackable;

namespace SnowEater.Core.StateMachine.GameState
{
    public abstract class GameStackableState : CoreState<GameStackableStateEnum>
    {
        // Services
        protected IWindowManagerService _windowManagerService;
        protected IStateMachineService _stateMachineService;

        protected GameStackableStateContext _gameStateContext;

        private readonly EnteredStateEvent<GameStackableStateEnum> _enteredStateEvent;


        protected GameStackableState(GameStackableStateContext gameStateContext, GameStackableStateEnum stateKey) : base(stateKey)
        {
            _windowManagerService = Systems.Services.Get<IWindowManagerService>();
            _stateMachineService = Systems.Services.Get<IStateMachineService>();

            _gameStateContext = gameStateContext;
            _enteredStateEvent = new EnteredStateEvent<GameStackableStateEnum>(StateKey);
        }

        public override void EnterState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            EventManager.TriggerEvent(_enteredStateEvent);
        }

        public override void ExitState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {

        }

        public override CoreState<GameStackableStateEnum> UpdateState(CoreStateMachine<GameStackableStateEnum> stateManager)
        {
            return this;
        }

        public override void ReturnFromSubState(CoreStateMachine<GameStackableStateEnum> stateManager, CoreState<GameStackableStateEnum> previousState)
        {
            base.ReturnFromSubState(stateManager, previousState);
            EnteredStateEvent<GameStackableStateEnum> enteredStateEvent = new EnteredStateEvent<GameStackableStateEnum>(StateKey);
            EventManager.TriggerEvent(enteredStateEvent);

        }
    }
}