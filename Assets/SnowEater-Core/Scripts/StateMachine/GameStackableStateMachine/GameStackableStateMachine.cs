
using SnowEater.Core.StateMachine.Stackable;

namespace SnowEater.Core.StateMachine.GameState
{
    public class GameStackableStateMachine : CoreStackableStateMachine<GameStackableStateEnum>
    {
        protected GameStackableStateContext _gameStateContext;

        protected override void Awake()
        {
            base.Awake();
            _gameStateContext = new GameStackableStateContext();
            InitializeStates();
        }

        protected virtual void InitializeStates()
        {
            _statesDictionary.Add(GameStackableStateEnum.TITLE_STATE, new TitleStackableState(_gameStateContext, GameStackableStateEnum.TITLE_STATE));
            _statesDictionary.Add(GameStackableStateEnum.GAMEPLAY_STATE, new GameplayStackableState(_gameStateContext, GameStackableStateEnum.GAMEPLAY_STATE));
            _statesDictionary.Add(GameStackableStateEnum.PAUSE_STATE, new PausedStackableState(_gameStateContext, GameStackableStateEnum.PAUSE_STATE));

            _startState = _statesDictionary[GameStackableStateEnum.TITLE_STATE];
        }
    }
}