using SnowEater.Core.Utils.ExpandableEnum;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace SnowEater.Core.StateMachine.Stackable
{
    public class CoreStackableStateMachine<TState> : CoreStateMachine<TState>
    {
        protected Stack<CoreState<TState>> _stateStack;

        protected virtual void Awake()
        {
            _stateStack = new Stack<CoreState<TState>>();
        }

        protected override void Start()
        {
            if (_startState != null)
            {
                PushState(_startState);
            }
        }
        protected override void Update()
        {
            if (_startState == null)
            {
                Debug.LogWarning($"Null state set in state machine: {gameObject.name}");
                PushState(_startState);
            }

            _currenState?.UpdateState(this);
        }

        public virtual void EnterSubState(CoreState<TState> substate)
        {
            if (substate == null)
            {
                Debug.LogError($"Trying to push null sub state onto stack. Current state: {_currenState}");
                return;
            }

            PushState(substate);
        }

        public virtual void ExitSubState()
        {
            CoreState<TState> previousState = PopState();

            _currenState.ReturnFromSubState(this, previousState);
        }

        protected void PushState(CoreState<TState> state)
        {
            _currenState = state;
            _stateStack.Push(state);
            state.EnterState(this);
        }

        protected CoreState<TState> PopState()
        {
            _currenState.ExitState(this);

            CoreState<TState> previous = _stateStack.Pop();

            if (_stateStack.Count == 0)
            {
                _currenState = null;
            }
            else
            {
                _currenState = _stateStack.Peek();
            }

            return previous;
        }

        public override void ChangeState(CoreState<TState> nextState)
        {
            if (_currenState != null)
            {
                PopState();
            }

            PushState(nextState);
        }
    }
}