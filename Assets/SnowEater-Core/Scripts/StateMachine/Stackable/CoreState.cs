using SnowEater.Core.StateMachine.GameState;
using UnityEngine;

namespace SnowEater.Core.StateMachine.Stackable
{
    public abstract class CoreState<TState>
    {
        protected CoreState(GameStackableStateEnum key)
        {
            StateKey = key;
        }
        public GameStackableStateEnum StateKey { get; private set; }
        public abstract void EnterState(CoreStateMachine<TState> stateManager);
        public abstract void ExitState(CoreStateMachine<TState> stateManager);
        public abstract CoreState<TState> UpdateState(CoreStateMachine<TState> stateManager);

        public virtual void ReturnFromSubState(CoreStateMachine<TState> stateManager, CoreState<TState> previousState)
        {
            Debug.LogWarning("Coming back from sub state in menu. Make sure this is intended!");
        }
    }
}