using SnowEater.Core.StateMachine.GameState;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace SnowEater.Core.StateMachine.Stackable
{
    public class CoreStateMachine<TState> : MonoBehaviour
    {
        protected readonly Dictionary<TState, CoreState<TState>> _statesDictionary = new Dictionary<TState, CoreState<TState>>();
        public Dictionary<TState, CoreState<TState>> StatesDictionary => _statesDictionary;

        [SerializeField]
        protected CoreState<TState> _startState;

        protected CoreState<TState> _currenState;

        protected virtual void Start()
        {
            ChangeState(_startState);
        }

        protected virtual void Update()
        {
            var nextState = _currenState.UpdateState(this);

            if (nextState != _currenState)
            {
                ChangeState(nextState);
            }

        }

        public virtual void ChangeState(CoreState<TState> nextState)
        {
            if (_currenState != null)
            {
                _currenState.ExitState(this);
            }

            _currenState = nextState;

            if (_currenState != null)
            {
                _currenState.EnterState(this);
            }
        }

        public virtual CoreState<TState> GetCurrentState()
        {
            return _currenState;
        }
    }
}