using System.Collections;
using UnityEngine;

namespace SnowEater.Core.Systems
{
    /// <summary>
    /// This was taken from the post: https://www.reddit.com/r/Unity3D/comments/j2kt0i/i_created_a_scriptableobject_class_that_can_use/
    ///
    /// It allows us to use Coroutines with Scriptable Objects. This helps with the WindowManager service.
    /// </summary>
    ///
    /// TODO: JP - Sept 28, 2023 - Clean this class up/give it a once over.
    public abstract class ScriptableObjectWithCoroutines : ScriptableObject
    {
        private CoroutineSurrogate _routiner;
        protected CoroutineSurrogate Routiner => _routiner != null ? _routiner : _routiner = CreateCoroutineSurrogate();
    
        protected Coroutine StartCoroutine(IEnumerator routine)
        {
            return Routiner.StartCoroutine(routine);
        }
    
        protected void StopCoroutine(Coroutine routine)
        {
            if (routine == null)
            {
                return;
            }
        
            Routiner.StopCoroutine(routine);
        }
    
        private CoroutineSurrogate CreateCoroutineSurrogate()
        {
            CoroutineSurrogate routiner = new GameObject(nameof(CoroutineSurrogate)+$"_{name}")
                .AddComponent<CoroutineSurrogate>();
            DontDestroyOnLoad(routiner);
            return routiner;
        }
    }
    public class CoroutineSurrogate : MonoBehaviour { }
}