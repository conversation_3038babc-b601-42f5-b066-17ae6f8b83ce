using System;

namespace SnowEater.Core.Systems
{
    [Flags]
    public enum ServicePlatform
    {
        iOS = 1,
        Desktop = 2,
        Android = 4,
    }
    public interface IService
    {
        public Type GetServiceInterface();
        public bool IsAvailable();
        public void OnInitialize();
        public void OnUninitialize();
        public void OnCreateProcessResources();
        public void OnDestroyProcessResources();
        public bool IsEnabled();
    }
    
    public abstract class Service : ScriptableObjectWithCoroutines, IService
    {
        public abstract Type GetServiceInterface();
        protected virtual ServicePlatform SupportedPlatforms => ServicePlatform.iOS | ServicePlatform.Desktop | ServicePlatform.Android;

        public bool IsAvailable()
        {
            bool supportedPlatform;
#if UNITY_EDITOR || UNITY_STANDALONE
            supportedPlatform = SupportedPlatforms.HasFlag(ServicePlatform.Desktop);
#elif UNITY_IOS

            supportedPlatform = SupportedPlatforms.HasFlag(ServicePlatform.iOS);
#elif UNITY_ANDROID
            supportedPlatform = SupportedPlatforms.HasFlag(ServicePlatform.Android);
#endif

            return supportedPlatform && IsEnabled();
        }
        
        public virtual bool IsReady() => true;

        public virtual void OnInitialize() { }

        public virtual void OnUninitialize() { }

        public virtual void OnCreateProcessResources() { }

        public virtual void OnDestroyProcessResources() { }
        public virtual bool IsEnabled() => true;
    }
}