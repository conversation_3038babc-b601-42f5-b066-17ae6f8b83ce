using System;
using System.Collections.Generic;
using SnowEater.Core.Exceptions;
using Object = UnityEngine.Object;

namespace SnowEater.Core.Systems
{
    // ServiceLocator interface that allows for NSubstitute to mock the ServiceLocator for testing
    // This allows for Services.Instance = Substitute.For<IServiceLocator>();
    // Which will enable us to mock up the dependencies of individual services via:
    // ExampleService = Substitute.For<IExampleService>();
    // Services.Get<IExampleService>().Returns(ExampleService);
    public interface IServiceLocator
    {
        T GetService<T>() where T : class, IService;
        IService GetService(Type type);
        bool HasService<T>() where T : class, IService;
        Dictionary<Type, IService> ServicesDictionary { get; }
        Service Add(Service serviceDefinition);
        void Remove(IService service);
        bool Contains(Service service);
        bool Contains(Type serviceType);
        bool Contains<T>() where T : Service;
    }
    
    /// <summary>
    /// Service Locator Pattern to avoid Singletons
    /// Services are referenced by interface (inherited from IService)
    /// This allows all services to be mocked for testing
    /// </summary>
    public class Services : IServiceLocator
    {
        public static event Action<IService> ServiceAdded; 
        public static event Action<IService> ServiceRemoved;
        public static event Action OnProcessReady;

        private readonly Dictionary<Type, IService> m_Services = new Dictionary<Type, IService>();
        public Dictionary<Type, IService> ServicesDictionary => m_Services;

        private static IServiceLocator s_Instance;
        
        public static IServiceLocator Instance
        {
            get => s_Instance;
            set => s_Instance = value;
        }
        
        // This is the primary access point to retrieve services
        // Designed to be as compact as possible:
        // Services.Get<IExampleService>().DoSomething();
        // Separating Get<> from GetService allows us to mock the return of this method
        // Services.Get<IExampleService>().Returns(ExampleService);
        public static T Get<T>() where T : class, IService
        {
            return Instance.GetService<T>();
        }
        /// <summary>
        /// Attempt to retrieve a service, returning true if successful and sets the out parameter <paramref name="service"/>
        /// Returns false if the service is not found and sets the out parameter <paramref name="service"/> to null
        /// </summary>
        /// <param name="service">The service of the specified type</param>
        /// <typeparam name="T">The type of the service being requested</typeparam>
        /// <returns></returns>
        public static bool TryGet<T>(out T service) where T : class, IService
        {
            if (Instance.HasService<T>())
            {
                service = Instance.GetService<T>();
                return true;
            }

            service = null;
            return false;
        }
        
        // This will be the secondary access pint to retrieve services
        // Designed to be as compact as possible:
        // Services.Get<IExampleService>().DoSomething();
        public T GetService<T>() where T : class, IService
        {
            return GetService(typeof(T)) as T;
        }

        /// This extension is required to grab a service that exists, but we don't have the instance type of already,
        /// and are unable to use generics due to dynamic restrictions.
        public IService GetService(Type type)
        {
            if(Instance.ServicesDictionary.TryGetValue(type, out var service))
            {
                return service;
            }
            throw new DependencyException($"Service of type {type} not found");
        }
        
        public static bool Has<T>() where T : class, IService
        {
            return Instance != null && Instance.HasService<T>();
        }
        public bool HasService<T>() where T : class, IService
        {
            return Instance.ServicesDictionary.ContainsKey(typeof(T));
        }
        /// <summary>
        /// Create the Services Singleton
        /// </summary>
        public static Services Create()
        {
            Instance ??= new Services();
            return Instance as Services;
        }
        
        /// <summary>
        /// Removes all dictionary entries and invalidates the Services singleton pointer
        /// </summary>
        public void Destroy()
        { 
            RemoveAll();
            s_Instance = null;
        }
        
        /// <summary>
        /// Updates all services that implement IUpdateable
        /// </summary>
        public void Update()
        {
            foreach (var service in m_Services.Values)
            {
                if (service is IUpdateable updateable)
                {
                    updateable.Update();
                }
            }
        }

        public void LateUpdate()
        {
            foreach (var service in m_Services.Values)
            {
                if (service is ILateUpdateable updateable)
                {
                    updateable.LateUpdate();
                }
            }   
        }

        public Service Add(Service serviceDefinition)
        {
            if(serviceDefinition == null)
            {
                throw new ArgumentNullException(nameof(serviceDefinition), "[serviceDefinition] cannot be null.");
            }

            if (!serviceDefinition.IsAvailable())
            {
                throw new DependencyException($"Service {serviceDefinition.GetType()} is not available and cannot be added.");
            }
            
            Service serviceInstance = Object.Instantiate(serviceDefinition);
            m_Services.Add(serviceInstance.GetServiceInterface(), serviceInstance);
            ServiceAdded?.Invoke(serviceInstance);
            
            serviceInstance.OnInitialize();

            return serviceInstance;
        }
        
        public void Add(ICollection<Service> serviceDefinitions)
        {
            foreach (var service in serviceDefinitions)
            {
                Add(service);
            }
        }
        public void Remove(IService service)
        {
            if (service == null)
            {
                throw new ArgumentNullException(nameof(service), "Cannot remove null service..");
            }
            
            if(!m_Services.TryGetValue(service.GetServiceInterface(), out var serviceInstance))
            {
                throw new DependencyException($"Service {service.GetType()} not found and cannot be removed.");
            }

            m_Services.Remove(service.GetServiceInterface());
            ServiceRemoved?.Invoke(serviceInstance);
            
            serviceInstance.OnUninitialize();
            
            Service serviceObject = serviceInstance as Service;
            if(serviceObject)
                Object.Destroy(serviceObject);
        }

        public void Remove(ICollection<IService> services)
        {
            foreach (var service in services)
            {
                Remove(service);
            }
        }
        public void RemoveAll()
        {
            var services = new IService[m_Services.Values.Count];
            m_Services.Values.CopyTo(services, 0);
            
            Remove(services);
        }

        public void CreateProcessResources(IService service)
        {
            if (service == null)
            {
                throw new ArgumentNullException(nameof(service), "Cannot create process resources for null service.");
            }
            
            if(!m_Services.TryGetValue(service.GetServiceInterface(), out var serviceInstance))
            {
                throw new DependencyException($"Service {service.GetType()} not found and cannot create process resources.");
            }
            
            serviceInstance.OnCreateProcessResources();
        }
        
        public void CreateProcessResources(ICollection<IService> serviceDefinitions)
        {
            foreach (var service in serviceDefinitions)
            {
                if (!service.IsAvailable())
                { 
                    throw new DependencyException($"Service {service.GetType()} is not available and cannot create process resources."); 
                }
                CreateProcessResources(service);
            }
            OnProcessReady?.Invoke();
        }
        
        public void DestroyProcessResources(IService service)
        {
            if (service == null)
            {
                throw new ArgumentNullException(nameof(service), "Cannot destroy process resources for null service.");
            }
            
            if(!m_Services.TryGetValue(service.GetServiceInterface(), out var serviceInstance))
            {
                throw new DependencyException($"Service {service.GetType()} not found and cannot destroy process resources.");
            }
            
            serviceInstance.OnDestroyProcessResources();
        }
        
        public void DestroyProcessResources(ICollection<IService> serviceDefinitions)
        {
            foreach (var service in serviceDefinitions)
            {
                if (!service.IsAvailable())
                { 
                    throw new DependencyException($"Service {service.GetType()} is not available and cannot destroy process resources."); 
                }
                DestroyProcessResources(service);
            }
        }


        public bool Contains(Service service) => m_Services.ContainsKey(service.GetServiceInterface());

        public bool Contains(Type serviceType) => m_Services.ContainsKey(serviceType);

        public bool Contains<T>() where T : Service => m_Services.ContainsKey(typeof(T));
    }
}