using SnowEater.Core.StateMachine.GameState;
using SnowEater.Core.StateMachine.Stackable;
using SnowEater.Core.Systems;

namespace SnowEater.Core.Services
{
    public interface IStateMachineService : IService
    {
        public CoreState<GameStackableStateEnum> CurrentState { get; }
        public void ChangeGameState(GameStackableStateEnum nextGameState);
        public void EnterGameSubState(GameStackableStateEnum nextGameState);
        public void ExitCurrentSubState();
    }
}