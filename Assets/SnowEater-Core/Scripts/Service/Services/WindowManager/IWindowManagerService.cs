using SnowEater.Core.Managers.WindowManager;
using SnowEater.Core.Systems;
using System;

namespace SnowEater.Core.Services
{
    public interface IWindowManagerService : IService
    {
        event Action<Type> OnWindowShowStarted;
        event Action<Type> OnWindowShowCompleted;
        event Action<Type> OnWindowHideStarted;
        event Action<Type> OnWindowHideCompleted;
        /* This has been commented out as is is currently not being used
        event Action<Type> OnWindowLoaded;
        event Action<Type> OnWindowUnloaded;
        */
        LayerData[] Layers { get; }
        bool IsReady { get; }

        void PushWindow<T>(string overrideLayer = null, Action<T> onComplete = null) where T : Window;
        void PushWindow(System.Type windowType, string overrideLayer = null, Action<Window> onComplete = null);
        void PushWindow<T, U>(U windowData, string overrideLayer = null, Action<T> onComplete = null) where T : Window<U> where U : WindowData;

        void PopWindow(string layerName);
        void PopAllWindowsInLayer(string layerId);

        void SetWindowData<T, U>(U windowData, T window = null) where T : Window<U> where U : WindowData;
        LayerInstance GetLayer(string layerName);

        string GetDefaultLayerIdOf<T>() where T : Window;

        void SetWindowManagerData(WindowManagerData data);
        }
}