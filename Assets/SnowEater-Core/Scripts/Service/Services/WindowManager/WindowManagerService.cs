using SnowEater.Core.Managers.WindowManager;
using SnowEater.Core.Systems;
using SnowEater.HelloWorld.UI;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using System.Collections;
using System.Data;
using UnityEngine.Serialization;

namespace SnowEater.Core.Services
{
    [CreateAssetMenu(
        fileName = nameof(WindowManagerService),
        menuName = "Snow Eater/Services/" + nameof(WindowManagerService))]
    public class WindowManagerService : Service, IWindowManagerService
    {
        public override Type GetServiceInterface() => typeof(IWindowManagerService);

        private const int DEFAULT_SORTING_ORDER = 5;

        [FormerlySerializedAs("Data")]
        [SerializeField]
        private WindowManagerData _data;

        public void SetWindowManagerData(WindowManagerData data)
        {
            _data = data;
        }

        /// <summary> Invoked when a <see cref="Window"/> starts its show routine </summary>
        public event Action<Type> OnWindowShowStarted;

        /// <summary> Invoked when a <see cref="Window"/> is finished its show routine </summary>
        public event Action<Type> OnWindowShowCompleted;

        /// <summary> Invoked when a <see cref="Window"/> starts its hide routine </summary>
        public event Action<Type> OnWindowHideStarted;

        /// <summary> Invoked when a <see cref="Window"/> is finished its hide routine </summary>
        public event Action<Type> OnWindowHideCompleted;

/* This has been commented out as is is currently not being used
        /// <summary> Invoked when a <see cref="Window"/> is loaded into memory </summary>
        public event Action<Type> OnWindowLoaded;

        /// <summary> Invoked when a <see cref="Window"/> is removed from memory </summary>
        public event Action<Type> OnWindowUnloaded;
*/
        /// <summary> The layer objects used by the manager</summary>
        public LayerData[] Layers => _data.Layers;

        public new bool IsReady => _layerDictionary.Count > 0;

        private readonly Dictionary<string, LayerInstance> _layerDictionary = new Dictionary<string, LayerInstance>();
        private readonly List<Window> _inactiveWindows = new List<Window>();

        public override void OnCreateProcessResources()
        {
	        base.OnCreateProcessResources();
	        SetUpDocument();
        }

        private void SetUpDocument()
        {
	        var windowManagerObject = new GameObject("WindowManager");
            var doc = windowManagerObject.AddComponent<UIDocument>();
            windowManagerObject.AddComponent<MainMenuUIManager>();
            doc.panelSettings = _data.PanelSettings;
            doc.sortingOrder = DEFAULT_SORTING_ORDER;
            // Will need to load this, likely create tooling to generate it.
            // Perhaps how we manage runtime layers will need to be pre-generated as well
            var container = new VisualElement()
            {
                name = "Container"
            };
            container.style.flexGrow = new StyleFloat(1f); // Ensure the container is expanded fully
            foreach (var layer in Layers)
            {
                var layerObject = new GameObject(layer.LayerName);
                layerObject.transform.SetParent(windowManagerObject.transform);
                var li = layerObject.AddComponent<LayerInstance>();
                var layerElement = li.Initialize(layer);
                container.Add(layerElement);
                _layerDictionary.Add(layer.LayerName, li);
            }
            doc.rootVisualElement.Add(container);
        }

        /// <summary>
		/// Creates a Window of type T and adds it to default (or override) layer.
		/// Will use a pooled Window of type T if one exists.
		/// </summary>
		/// <param name="overrideLayer"></param>
		/// <param name="onComplete"></param>
		/// <typeparam name="T"></typeparam>
		public void PushWindow<T>(string overrideLayer = null, Action<T> onComplete = null) where T : Window
		{
			StartCoroutine(PushWindowRoutine(typeof(T), overrideLayer, window => onComplete?.Invoke(window as T)));
		}

		public void PushWindow(Type windowType, string overrideLayer = null, Action<Window> onComplete = null)
		{
			StartCoroutine(PushWindowRoutine(windowType, overrideLayer, onComplete));
		}

		/// <summary>
		/// Creates a Window of type T with data type U and adds it to default (or override) layer.
		/// Only use if you need to inject data into a Window when shown.
		/// Will use a pooled Window of type T if one exists.
		/// </summary>
		/// <param name="windowData"></param>
		/// <param name="overrideLayer"></param>
		/// <param name="onComplete"></param>
		/// <typeparam name="T"></typeparam>
		/// <typeparam name="U"></typeparam>
		public void PushWindow<T, U>(U windowData, string overrideLayer = null, Action<T> onComplete = null) where T : Window<U> where U : WindowData
		{
			StartCoroutine(PushWindowRoutine<T, U>(windowData, overrideLayer, onComplete));
		}

		/// <summary>
		/// IEnumerator that handles the async loading and showing of windows
		/// </summary>
		/// <param name="windowType"></param>
		/// <param name="overrideLayer"></param>
		/// <param name="onComplete"></param>
		/// <typeparam name="T"></typeparam>
		/// <returns></returns>
		/// <exception cref="NullReferenceException"></exception>
		/// <exception cref="ArgumentException"></exception>
		private IEnumerator PushWindowRoutine(Type windowType, string overrideLayer = null, Action<Window> onComplete = null)
		{
			var windowHolder = _data.GetWindowHolder(windowType);
			if (windowHolder == null)
			{
				throw new DataException($"Could not find WindowHolder for Window Type {windowType}");
			}

			var windowObject = GetInactiveWindow(windowType);
			if (windowObject == null)
			{
				yield return windowHolder.LoadWindow();

				windowObject = windowHolder.Window;

				if (windowObject == null)
				{
					throw new NullReferenceException($"Window prefab for {windowType} is null");
				}
			}

			var layer = string.IsNullOrEmpty(overrideLayer) ? GetLayer(windowHolder.LayerName) : GetLayer(overrideLayer);

			OnWindowShowStarted?.Invoke(windowType);

			layer.Layer.Add(windowObject);
			windowObject.SetEnabled(true);
			windowObject.RequestDestroyWindow += FinishHidingWindow;
			windowObject.FinishedShowAnimation += FinishedShowWindowAnimation;

			if (!layer.PushWindow(windowObject))
			{
				Debug.LogWarning($"Layer {layer.name} cannot have multiple of same type of window. Will not add {windowType}");
				layer.Layer.Remove(windowObject);
				onComplete?.Invoke(null);
				yield break;
			}

			windowObject.CurrentLayerData = layer;
			windowObject.ShowWindow();
			onComplete?.Invoke(windowObject);
			yield return null;
		}

		/// <summary>
		/// IEnumerator that handles the async loading and showing of windows with data
		/// </summary>
		/// <param name="windowData"></param>
		/// <param name="overrideLayer"></param>
		/// <param name="onComplete"></param>
		/// <typeparam name="T"></typeparam>
		/// <typeparam name="U"></typeparam>
		/// <returns></returns>
		private IEnumerator PushWindowRoutine<T, U>(U windowData, string overrideLayer = null, Action<T> onComplete = null) where T : Window<U> where U : WindowData
		{
			yield return PushWindowRoutine(typeof(T), overrideLayer, (window) =>
			{
				T w = window as T;
				if (w != null)
				{
					w.SetWindowData(windowData);
				}
				onComplete?.Invoke(w);
			});
		}

		private Window GetInactiveWindow(System.Type type)
		{
			Window toReturn = null;

			foreach (var window in _inactiveWindows)
			{
				if (window.GetType().IsAssignableFrom(type))
				{
					toReturn = window;
					break;
				}
			}

			_inactiveWindows.Remove(toReturn);
			return toReturn;
		}

		/// <summary>
		/// Hides the first Window of type T that is found
		/// </summary>
		/// <param name="layerName"></param>
		public void PopWindow(string layerName)
		{
			var layer = GetLayer(layerName);
			var window = layer.PopWindow();

			if (window != null)
			{
				window.CurrentLayerData = null;
				StartHidingWindow(window);
			}
		}

		/// <summary>
		/// Remove all of the <see cref="Window"/>s on a given layer
		/// </summary>
		/// <param name="layerId"></param>
		public void PopAllWindowsInLayer(string layerId)
		{
			var layer = GetLayer(layerId);

			// The windows remove themselves from the layer.Windows list on HideWindow, so let's pop them backwards so
			// we don't cause any errors, and save us from duplicating the list.
			for (var i = layer.Windows.Count - 1; i >= 0; i--)
			{
				layer.Windows[i].HideWindow();
			}

			layer.PopAllWindows();
		}

		/// <summary>
		/// Set the data for window to windowData.
		/// If no window provided then set the data of the first window of type T found in the scene.
		/// </summary>
		/// <param name="windowData"></param>
		/// <param name="window"></param>
		/// <typeparam name="T"></typeparam>
		/// <typeparam name="U"></typeparam>
		public void SetWindowData<T, U>(U windowData, T window = null) where T : Window<U> where U : WindowData
		{
			// If passing in a referenced window just set the data and get out!
			if (window != null)
			{
				window.SetWindowData(windowData);
				return;
			}

			// Find the first window of type in scene
			var foundWindow = FindWindowInLayers<T>();

			if (foundWindow == null)
			{
				throw new NullReferenceException($"No Window of type {typeof(T)} found in scene!");
			}

			foundWindow.SetWindowData(windowData);
		}


		/// <summary>
		/// Get a layer by its name
		/// </summary>
		/// <param name="layerName"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentException"></exception>
		public LayerInstance GetLayer(string layerName)
		{
			if (!_layerDictionary.ContainsKey(layerName))
			{
				throw new ArgumentException($"WindowManager does not have a layer with name '{layerName}'");
			}

			return _layerDictionary[layerName];
		}

		/// <summary>
		/// Should only be used internally and be called only when a Window is hidden from PopWindow or PopAllWindowsOfType
		/// Tells window to start its hiding transition if it has one, if not or when finihed then the window will
		/// call FinishHidingWindow by invoking RequestDestroy event
		/// </summary>
		/// <param name="window"></param>
		private void StartHidingWindow(Window window)
		{
			OnWindowHideStarted?.Invoke(window.GetType());
			window.HideWindow();
		}

		/// <summary>
		/// Called to clean up a Window after it finishes its hiding transition. Or if it has none is called right away.
		/// </summary>
		/// <param name="window"></param>
		private void FinishHidingWindow(Window window)
		{
			var type = window.GetType();
			window.RequestDestroyWindow -= FinishHidingWindow;
			// Setting inactive because lazy load
			window.SetEnabled(false);
			// In the case we are hiding because the Window has requested, we need to make sure we removed the window from the stack
			window.CurrentLayerData?.RemoveWindow(window);
			window.CurrentLayerData = null;

			_inactiveWindows.Add(window);
			OnWindowHideCompleted?.Invoke(type);
		}

		private void FinishedShowWindowAnimation(Window window)
		{
			window.FinishedShowAnimation -= FinishedShowWindowAnimation;
			OnWindowShowCompleted?.Invoke(window.GetType());
		}

		private T FindWindowInLayers<T>() where T : Window
		{
			foreach (var layer in _layerDictionary)
			{
				var window = layer.Value.GetWindow<T>();
				if (window != null)
				{
					return window as T;
				}
			}

			return null;
		}

		/// <summary>
		/// Get the default layer name for a window of the given type
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <returns></returns>
		public string GetDefaultLayerIdOf<T>() where T : Window
		{
			return _data.GetWindowHolder(typeof(T)).LayerName;
		}
    }
}