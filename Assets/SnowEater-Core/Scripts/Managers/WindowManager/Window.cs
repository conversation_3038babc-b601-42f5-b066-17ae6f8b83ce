using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.Managers.WindowManager
{
	/// <summary>
	/// A window managed by the <see cref="WindowManager"/>
	/// </summary>
	public class Window : VisualElement
	{
		/// <summary> Invoked when the window should be destroyed by the manager </summary>
		public event System.Action<Window> RequestDestroyWindow;

		/// <summary> Invoked when the "Show" animation is complete </summary>
		public event System.Action<Window> FinishedShowAnimation;

		/// <summary> Invoked when the "Hide" animation is complete </summary>
		public event System.Action<Window> FinishedHideAnimation;

		/// <summary> The layer that ths window is currently being displayed on </summary>
		[HideInInspector]
		public LayerInstance CurrentLayerData = null;

		/// <summary>
		/// Show the window
		/// </summary>
		public virtual void ShowWindow()
		{
			OnBeforeTransitionIn();

			FinishedShowAnimation?.Invoke(this);
		}

		/// <summary>
		/// Triggered before the window transitions into view
		/// </summary>
		protected virtual void OnBeforeTransitionIn() { }

		/// <summary>
		/// Hide the window
		/// </summary>
		public virtual void HideWindow()
		{
			OnBeforeTransitionOut();
			FinishedHideAnimation?.Invoke(this);
			RequestDestroyWindow?.Invoke(this);
		}

		/// <summary>
		/// Triggered before the window transitions out of view
		/// </summary>
		protected virtual void OnBeforeTransitionOut() { }

	}

	/// <summary>
	/// Generically typed window
	/// </summary>
	/// <typeparam name="T"> The data type for this Window </typeparam>
	public class Window<T> : Window where T : WindowData
	{
		private T m_WindowData;

		/// <summary> The data object for the window </summary>
		protected T WindowData => m_WindowData;

		/// <summary>
		/// Set the window data object
		/// </summary>
		/// <param name="windowData"></param>
		public virtual void SetWindowData(T windowData)
		{
			m_WindowData = windowData;
		}
	}
}