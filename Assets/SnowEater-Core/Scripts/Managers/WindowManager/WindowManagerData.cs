using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.Managers.WindowManager
{
	/// <summary>
	/// Data for the <see cref="WindowManager"/>
	/// </summary>
	[CreateAssetMenu(menuName = "Snow Eater/" + nameof(WindowManagerData))]
	public class WindowManagerData : ScriptableObject
	{
		[SerializeField]
		protected PanelSettings m_PanelSettings;
		public PanelSettings PanelSettings => m_PanelSettings;

		[SerializeField]
		protected List<WindowHolder> m_WindowHolders;

		/// <summary> The <see cref="WindowHolder"/> objects </summary>
		public List<WindowHolder> WindowHolders => m_WindowHolders;

		[SerializeField]
		protected LayerData[] m_Layers;

		/// <summary> The <see cref="LayerData"/>s available </summary>
		public LayerData[] Layers => m_Layers;

		/// <summary>
		/// Static accessor for <see cref="LayerData"/>s. Used for editor functionality.
		/// </summary>
		public static LayerData[] WindowLayers;

		protected virtual void OnValidate()
		{
			bool isDirty = false;
			// TODO Validate Layers
			// WindowHolders that use AssetRef should have prefab reference removed so they aren't loaded into memory
			foreach (var windowHolder in m_WindowHolders)
			{
				isDirty = isDirty || windowHolder.OnValidate();
				if (windowHolder.UseAssetRef)
				{
					windowHolder.ResetWindow();
				}
			}

			if (isDirty)
			{
#if UNITY_EDITOR
				UnityEditor.EditorUtility.IsDirty(this);
#endif
			}
		}

		/// <summary>
		/// Get the Window prefab for a Window of type T. Prefabs defined in WindowManagerData inspector
		/// </summary>
		/// <typeparam name="T">Type of Window subclass you want to get</typeparam>
		/// <returns></returns>
		public Window GetWindow<T>() where T : Window
		{
			foreach (var windowHolder in m_WindowHolders)
			{
				if (windowHolder.IsType(typeof(T)))
				{
					return windowHolder.Window;
				}
			}

			return null;
		}

		/// <summary>
		/// Get the WindowHolder for a Window of type T
		/// </summary>
		/// <typeparam name="T">Type of Window subclass you want to get</typeparam>
		/// <returns></returns>
		public WindowHolder GetWindowHolder(System.Type windowType)
		{
			foreach (var windowHolder in m_WindowHolders)
			{
				if (windowHolder.IsType(windowType))
				{
					return windowHolder;
				}
			}

			return null;
		}
	}
}