using System.IO;
using UnityEngine;

namespace SnowEater.Core.Events.Helpers
{
	/// <summary>
	/// Wrapper for the <see cref="System.Type"/> class that allows for serialization
	/// </summary>
	[System.Serializable]
	internal sealed class SerializableType : ISerializationCallbackReceiver
	{
		/// <summary> The type to be serialized </summary>
		public System.Type type;
		/// <summary> The data being serialized </summary>
		public byte[] data;
		public bool isValid;

		/// <summary>
		/// ctor
		/// </summary>
		/// <param name="aType"></param>
		public SerializableType(System.Type aType)
		{
			type = aType;
			isValid = true;
		}

		private static System.Type Read(BinaryReader aReader)
		{
			var paramCount = aReader.ReadByte();
			if (paramCount == 0xFF)
				return null;
			var typeName = aReader.ReadString();
			var type = System.Type.GetType(typeName);
			if (type == null)
				throw new System.Exception("Can't find type; '" + typeName + "'");
			if (type.IsGenericTypeDefinition && paramCount > 0)
			{
				var p = new System.Type[paramCount];
				for (int i = 0; i < paramCount; i++)
				{
					p[i] = Read(aReader);
				}
				type = type.MakeGenericType(p);
			}
			return type;
		}

		private static void Write(BinaryWriter aWriter, System.Type aType)
		{
			if (aType == null)
			{
				aWriter.Write((byte)0xFF);
				return;
			}
			if (aType.IsGenericType)
			{
				var t = aType.GetGenericTypeDefinition();
				var p = aType.GetGenericArguments();
				aWriter.Write((byte)p.Length);
				aWriter.Write(t.AssemblyQualifiedName);
				for (int i = 0; i < p.Length; i++)
				{
					Write(aWriter, p[i]);
				}
				return;
			}
			aWriter.Write((byte)0);
			aWriter.Write(aType.AssemblyQualifiedName);
		}

		/// <summary>
		/// Triggered immediately before serialization occurs
		/// </summary>
		public void OnBeforeSerialize()
		{
			using (var stream = new MemoryStream())
			{
				using (var w = new BinaryWriter(stream))
				{
					Write(w, type);
					data = stream.ToArray();
				}
			}
		}

		/// <summary>
		/// Triggered immediately after deserialization occurs
		/// </summary>
		public void OnAfterDeserialize()
		{
			using (var stream = new MemoryStream(data))
			{
				using (var r = new BinaryReader(stream))
				{
					try
					{
						type = Read(r);
						isValid = true;
					}
					catch
					{
						isValid = false;
					}
				}
			}
		}
	}

}