using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

namespace SnowEater.Core.Managers.WindowManager
{
	/// <summary>
	/// A layer for displaying windows
	/// </summary>
	[Serializable]
	public class LayerData
	{
		[SerializeField]
		protected string m_LayerName;
		/// <summary> The name of the layer </summary>
		public string LayerName => m_LayerName;

		[SerializeField, Tooltip("If a layer is sticky it will NOT hide on HideAllLayers unless the forceAll override is true")]
		protected bool m_IsSticky;
		/// <summary> Whether the layer will be hidden as a result of a <see cref="WindowManager.HideAllLayers"/> call </summary>
		public bool IsSticky => m_IsSticky;

		[SerializeField]
		protected bool m_CanHaveMultipleOfSameWindow = false;

		/// <summary> Whether the window can have more than one instance per layer </summary>
		public bool CanHaveMultipleOfSameWindow => m_CanHaveMultipleOfSameWindow;
	}

}