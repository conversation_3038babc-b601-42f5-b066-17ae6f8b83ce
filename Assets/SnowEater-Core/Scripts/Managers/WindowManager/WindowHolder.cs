using System;
using System.Collections;
using SnowEater.Core.Events.Helpers;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.Serialization;
using UnityEngine.UIElements;

namespace SnowEater.Core.Managers.WindowManager
{
    [Serializable]
    public class AssetReferenceVisualTreeAsset : AssetReferenceT<VisualTreeAsset>
    {
        /// <summary>
        /// Constructs a new reference to a GameObject.
        /// </summary>
        /// <param name="guid">The object guid.</param>
        public AssetReferenceVisualTreeAsset(string guid) : base(guid) {}
    }
	/// <summary>
	/// WindowHolder is a class WindowManagerData uses to know what to do with each Window subclass (prefab, load from addressables, etc)
	/// </summary>
	[Serializable]
	public class WindowHolder
	{
		/// <summary> The <see cref="Window"/> reference for this holder </summary>
		public Window Window => m_Window;
		public string LayerName => m_LayerName;
		[Header("Only one instance needs to be set")]
		[SerializeField]
		protected Window m_Window;
		[SerializeField]
		protected string m_LayerName;
		[SerializeField]
		protected VisualTreeAsset WindowUIDocument;
		[SerializeField]
		// <summary> The asset reference for the <see cref="Window"/> if an Addressables approach is being used </summary>
		protected AssetReferenceVisualTreeAsset m_AssetRef;
		[SerializeField, HideInInspector]
		private SerializableType m_WindowType;
		[FormerlySerializedAs("m_PrefabReference")]
		[SerializeField, HideInInspector]
		private string m_PrefabGUIDReference;

		/// <summary>
		/// Whether to use the supplied <see cref="m_AssetRef"/>
		/// </summary>
		public bool UseAssetRef => (m_AssetRef != null && !string.IsNullOrEmpty(m_AssetRef.AssetGUID));

		public bool IsType(System.Type windowType) => m_WindowType.type.IsAssignableFrom(windowType);

		public Type GetWindowType() => m_WindowType.type;

		/// <summary>
		/// Validate the data
		/// </summary>
		public bool OnValidate()
		{
			if (m_WindowType != null && !m_WindowType.isValid)
			{
				m_WindowType = null;
				m_PrefabGUIDReference = string.Empty;
			}
#if UNITY_EDITOR
			if (WindowUIDocument != null)
			{
				if (!UnityEditor.AssetDatabase.TryGetGUIDAndLocalFileIdentifier(WindowUIDocument, out string guid, out long indent))
				{
					throw new System.Exception("Cannot use non-prefab reference");
				}
				if (m_WindowType == null
				    || guid != m_PrefabGUIDReference)
				{
					m_PrefabGUIDReference = guid;
					var windowRoot = WindowUIDocument.Instantiate();
					var window = GetWindow(windowRoot);
					m_WindowType =  new SerializableType(window.GetType());
					window.RemoveFromHierarchy();
					return true;
				}
			}

			if (m_AssetRef != null && m_AssetRef.editorAsset != null && (m_WindowType == null || m_PrefabGUIDReference != m_AssetRef.AssetGUID))
			{
				var go = m_AssetRef.editorAsset as VisualTreeAsset;
				m_PrefabGUIDReference = m_AssetRef.AssetGUID;
				var ve = go.Instantiate();
				Window w = GetWindow(ve);
				if(w == null)
				{
					Debug.LogError($"Asset Reference {m_AssetRef.editorAsset.name} - {m_AssetRef.SubObjectName} does not contain type of Window Visual Element");
				}
				else
				{
					m_WindowType = new SerializableType(w.GetType());
				}

				return true;
			}
#endif
			return false;
		}

		private Window GetWindow(VisualElement ve)
		{
			Window w = ve.GetFirstOfType<Window>();
			foreach (var child in ve.Children())
			{
				if (w != null) break;
				w = child.GetFirstOfType<Window>();
			}

			return w;
		}

		/// <summary>
		/// Load the window asynchronously
		/// </summary>
		/// <returns></returns>
		/// <exception cref="NullReferenceException"></exception>
		public virtual IEnumerator LoadWindow()
		{
			if (!UseAssetRef)
			{
				var windowRoot = WindowUIDocument.Instantiate();
				m_Window = GetWindow(windowRoot);
				m_WindowType =  new SerializableType(m_Window.GetType());
			}
			if(m_Window != null)
			{
				yield break;
			}


			if (!m_AssetRef.RuntimeKeyIsValid())
			{
				throw new NullReferenceException($"AssetRef is not set");
			}

			var handle = m_AssetRef.LoadAssetAsync<UIDocument>();
			yield return handle;

			if (handle.Status == AsyncOperationStatus.Succeeded)
			{
				m_Window = handle.Result.visualTreeAsset.Instantiate().GetFirstOfType<Window>();
				m_WindowType =  new SerializableType(m_Window.GetType());
			}
			else
			{
				Debug.LogError($"Loading asset for {m_AssetRef.SubObjectName} was not successful with handle status of {handle.Status}");
			}
		}

		/// <summary>
		/// Unload the window
		/// </summary>
		public virtual void UnloadWindow()
		{
			if (!UseAssetRef)
			{
				return;
			}

			ResetWindow();
			m_AssetRef.ReleaseAsset();
		}

		/// <summary>
		/// Reset the window data
		/// </summary>
		public virtual void ResetWindow()
		{
			m_Window = null;
		}

	}
}