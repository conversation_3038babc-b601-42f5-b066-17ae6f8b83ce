using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Core.Managers.WindowManager
{
	public class LayerInstance : MonoBehaviour
	{
		public VisualElement Layer;
		public List<Window> Windows => m_Windows;
		public LayerData Data => m_Data;
		public int WindowCount => m_Windows.Count;
		public Transform Transform { get; protected set; }
		protected LayerData m_Data;
		protected List<Window> m_Windows = new();

		public VisualElement Initialize(LayerData data)
		{
			Transform = transform;
			m_Data = data;
			Layer = new VisualElement
			{
				pickingMode = PickingMode.Ignore
			};

#if UNITY_EDITOR
			name = data.LayerName;
			Layer.name = data.LayerName;
#endif
			Layer.AddToClassList("Layer");
			return Layer;
		}

		public bool PushWindow(Window window)
		{
			if (!m_Data.CanHaveMultipleOfSameWindow && HasWindow(window.GetType()))
			{
				Debug.LogWarning($"Layer {name} cannot have multiple of same type of window. Will not add {window.GetType()}");
				return false;
			}

			m_Windows.Add(window);
			if (!Layer.Contains(window))
			{
				Layer.Add(window);

			}
			window.visible = true;
			//TODO -- Cramer -- Should this be dependent on the window data? For example, should the TextCelebration be a blocking window?
			// window.pickingMode = PickingMode.Position;
			window.style.display = DisplayStyle.Flex;

			return true;
		}

		public Window PopWindow()
		{
			if (m_Windows.Count == 0)
			{
				Debug.LogWarning($"Cannot PopWindow on Layer {name} when m_Windows is empty!");
				return null;
			}

			var lastIndex = m_Windows.Count - 1;
			var window = m_Windows[lastIndex];
			RemoveWindow(window);
			return window;
		}

		/// <summary>
		/// Returns true if the layer has a window of the specified type
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <returns></returns>
		public virtual bool HasWindow<T>() where T : Window
		{
			return HasWindow(typeof(T));
		}

		/// <summary>
		/// Returns true if the layer has a window of the specified type
		/// </summary>
		/// <param name="t"></param>
		/// <returns></returns>
		public virtual bool HasWindow(System.Type t)
		{
			return GetWindow(t) != null;
		}

		/// <summary>
		/// Remove all windows from the layer
		/// </summary>
		public virtual void PopAllWindows()
		{
			// TODO properly remove windows
			m_Windows.Clear();
		}

		/// <summary>
		/// Get a window by type from the layer if one exists
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <returns></returns>
		public virtual Window GetWindow<T>() where T : Window
		{
			return GetWindow(typeof(T));
		}

		protected virtual Window GetWindow(System.Type t)
		{
			foreach (var w in m_Windows)
			{
				if (t.IsInstanceOfType(w))
				{
					return w;
				}
			}

			return null;
		}

		/// <summary>
		/// Removes the specified window from the layer
		/// </summary>
		/// <param name="window"></param>
		public virtual void RemoveWindow(Window window)
		{
			window.visible = false;
			//TODO -- Cramer -- Should this be dependent on the window data? For example, should the TextCelebration be a blocking window?
			// window.pickingMode = PickingMode.Ignore;
			window.style.display = DisplayStyle.None;
			Layer.Remove(window);
			m_Windows.Remove(window);
		}
	}

}