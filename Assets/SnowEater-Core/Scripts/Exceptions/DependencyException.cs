using System;

namespace SnowEater.Core.Exceptions
{
    /// <summary>
    /// Exception thrown when a Service locator request fails to resolve to a registered service.
    /// </summary>
    public class DependencyException : Exception
    {
        public DependencyException() { }
        public DependencyException(string message) : base(message) { }
        public DependencyException(string message, Exception inner) : base(message, inner) { }
    }
}