using System.Collections.Generic;
using UnityEngine;

namespace SnowEater.Core.Systems.Processes
{
    /// <summary>
    /// Process is a base class for creating custom processes that can be pushed onto a ProcessStack.
    /// Processes can define required services, which will be added to the Services object of the ProcessStack when the process is activated.
    /// Subclasses can override the OnInitialize, OnUninitialize, OnActivate, and OnDeactivate methods to define their behavior.
    /// </summary>
    public class Process : ScriptableObject
    {
        public static readonly string Path = System.IO.Path.Combine("Assets", "Definitions", "Processes");

        protected ProcessStack Stack;
        
        [SerializeField]
        protected Service[] m_ServiceDefinitions;
        
        protected Service[] m_AvailableServiceDefinitions;
        
        public virtual void OnInitialize(ProcessStack stack)
        {
            Stack = stack;
            m_AvailableServiceDefinitions = m_ServiceDefinitions;
            Stack.Services.Add(m_AvailableServiceDefinitions);
        }
        
        public virtual void OnUninitialize()
        {
            Stack.Services.Remove(m_AvailableServiceDefinitions);
        }

        protected Service[] AvailableServices()
        {
            var servicesList = new List<Service>();
            foreach (var service in m_ServiceDefinitions)
            {
                if (service == null)
                {
                    Debug.LogError($"Missing service definition for {name}");
                    continue;
                }
                if (service.IsAvailable())
                {
                    servicesList.Add(service);
                }
            }
            return servicesList.ToArray();
        }

        public virtual void OnActivate()
        {
            Stack.Services.CreateProcessResources(m_AvailableServiceDefinitions);
        }
        
        public virtual void OnDeactivate()
        {
            Stack.Services.DestroyProcessResources(m_AvailableServiceDefinitions);
        }

        public virtual void Update() { }
    }
}