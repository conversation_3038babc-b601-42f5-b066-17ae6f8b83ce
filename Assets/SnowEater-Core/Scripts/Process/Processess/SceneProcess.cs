using System;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace SnowEater.Core.Systems.Processes
{
    /// <summary>
    /// A process that loads a scene after initialization of its pre-requisite services have been completed.
    /// </summary>
    [CreateAssetMenu(menuName = "Snow Eater/Processes/Scene")]
    public class SceneProcess : Process
    {
        [SerializeField]
        private string m_SceneName;

        public string SceneName => m_SceneName;

        Scene m_InitScene;
        private Scene m_PreviousScene;

        // Ensure that the scene is loaded before the process is activated
        private bool m_LoadingInProgress;

        public override void OnActivate()
        {
            // Subscribe to the Scene Manager events
            SceneManager.sceneLoaded += OnSceneLoaded;
            SceneManager.sceneUnloaded += OnSceneUnloaded;

            m_PreviousScene = SceneManager.GetActiveScene();

            if(!m_PreviousScene.IsValid())
                throw new Exception("Can't find the active scene!");

            // Create an empty and temporary scene to perform
            // pre=loading operations (e.g Instantiate services and prefabs)
            m_InitScene = SceneManager.CreateScene("Service Init Scene");

            if(!m_InitScene.IsValid())
                throw new Exception("Can't create a new scene!");

            m_LoadingInProgress = true;

            SceneManager.SetActiveScene(m_InitScene);

            // Pre-loading operations
            Stack.Services.CreateProcessResources(m_AvailableServiceDefinitions);

            SceneManager.UnloadSceneAsync(m_PreviousScene);
        }


        public override void OnDeactivate()
        {
            Stack.Services.DestroyProcessResources(m_AvailableServiceDefinitions);

            // Unsubscribe from the Scene Manager events
            SceneManager.sceneLoaded -= OnSceneLoaded;
            SceneManager.sceneUnloaded -= OnSceneUnloaded;
        }

        protected virtual void OnSceneLoaded(Scene scene, LoadSceneMode mode)
        {
            if (m_LoadingInProgress && scene.name.Equals(SceneName, StringComparison.OrdinalIgnoreCase))
            {
                // When loading of the actual scene is completed,
                // merge the init scene into the actual scene
                SceneManager.MergeScenes(m_InitScene, scene);

                m_LoadingInProgress = false;
            }
        }
        protected virtual void OnSceneUnloaded(Scene scene)
        {
            if (m_LoadingInProgress && scene == m_PreviousScene)
            {
                // When unloading of previous scene is finished,
                // load the actual scene specified by SceneName
                SceneManager.LoadScene(SceneName, LoadSceneMode.Additive);
            }
        }

    }
}