using UnityEngine;

namespace SnowEater.Core.Systems.Processes
{
    [CreateAssetMenu(fileName = nameof(TestInstantiatorProcess), menuName = "Snow Eater/Processes/" + nameof(TestInstantiatorProcess))]
    public class TestInstantiatorProcess : Process
    {
        [SerializeField] private GameObject m_TestSubjectPrefab;
        [SerializeField] private GameObject m_TestSubject;

        public override void OnActivate()
        {
            base.OnActivate();
            m_TestSubject = Instantiate(m_TestSubjectPrefab);
        }

        public override void OnDeactivate()
        {
            base.OnDeactivate();
            if(m_TestSubject != null)
                Destroy(m_TestSubject);
        }
    }
}