using UnityEngine;

namespace SnowEater.Core.Systems.Processes
{
    [CreateAssetMenu(menuName = "Snow Eater/Processes/Boot")]
    public class BootProcess : Process
    {
        [SerializeField, Min(0)]
        float m_WaitTime = 1f;

        [SerializeField]
        public Process m_NextProcess;

        private float m_NextProcessTime;

        private bool m_InitDone;

        // Services that are required by this process go here
        // e.g IPlatformUtilsService m_PlatformUtilsService;

        public override void OnInitialize(ProcessStack stack)
        {
            base.OnInitialize(stack);

            // Initialize services here
            // e.g m_PlatformUtilsService = Services.Get<IPlatformUtilsService>();
            m_InitDone = true;
        }

        public override void OnActivate()
        {
            base.OnActivate();

            // Time until loading the next process
            m_NextProcessTime = Time.time + m_WaitTime;
        }

        public override void Update()
        {
            if (!m_InitDone)
            {
                return;
            }
            base.Update();

            if (m_NextProcessTime > 0 && Time.time > m_NextProcessTime)
            {
                // Check if everything is ready to load the next process
                // e.g if (m_PlatformUtilsService.IsReady())

                m_NextProcessTime = 0;

                Stack.Push(m_NextProcess);
            }
        }
    }
}