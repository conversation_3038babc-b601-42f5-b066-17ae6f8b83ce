using System;
using System.Collections.Generic;
using UnityEngine;

namespace SnowEater.Core.Systems.Processes
{
    /// <summary>
    /// The Process Stack provides a way for flow control between screens in the game.
    /// Processes can be pushed onto the stack, which will pause the current process and begin the new one.
    /// When a process is finished, it can be popped off the stack and the previous process will resume.
    /// The class also provides a set of Services that can be used by Processes to access common functionality.
    /// </summary>
    public class ProcessStack : MonoBehaviour
    {
        [SerializeField]
        [Tooltip("First process to push onto the stack.")]
        public Process m_InitialProcess;

        readonly List<Process> m_ProcessStack = new List<Process>(10);

        public int StackCount => m_ProcessStack.Count;

        public Services Services { get; private set; }

        public static ProcessStack Instance { get; private set; }

        private Process m_CurrentProcess => Peek();

        private Dictionary<Type, IService> services => Services?.ServicesDictionary;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                transform.parent = null;
                DontDestroyOnLoad(gameObject);

                Services = Services.Create();
            }
            else
            {
                Debug.LogWarning($"Destroying duplicate Process Stack instance {name}");
                Destroy(this);
            }
        }

        private void Start()
        {
            if (m_InitialProcess != null)
            {
                Push(m_InitialProcess);
            }
        }

        private void OnDestroy()
        {
            PopAll();
            Instance = null;

            Services?.Destroy();
        }

        public void Update()
        {
            Services.Update();

            if (m_ProcessStack.Count > 0)
            {
                m_ProcessStack[^1].Update();
            }
        }

        public void LateUpdate()
        {
            Services.LateUpdate();
        }

        private Process Peek()
        {
            if (m_ProcessStack.Count > 0)
            {
                return m_ProcessStack[^1];
            }
#if UNITY_EDITOR
            if (Application.isPlaying) return null;
#endif
            throw new NullReferenceException("Process Stack is empty");
        }

        public Process Push(Process processDefinition)
        {
            if (processDefinition == null)
            {
                throw new ArgumentNullException(nameof(processDefinition), "Process definition cannot be null");
            }

            int index = m_ProcessStack.Count - 1;
            if (index >= 0)
            {
                m_ProcessStack[index].OnDeactivate();
            }

            Process processInstance = Instantiate(processDefinition);
            m_ProcessStack.Add(processInstance);
            processInstance.OnInitialize(this);
            processInstance.OnActivate();

            return processInstance;
        }

        public T Push<T> () where T : Process
        {
            int index = m_ProcessStack.Count - 1;
            if (index >= 0)
            {
                m_ProcessStack[index].OnDeactivate();
            }

            T processInstance = Instantiate(ScriptableObject.CreateInstance<T>());
            m_ProcessStack.Add(processInstance);
            processInstance.OnInitialize(this);
            processInstance.OnActivate();

            return processInstance;
        }

        public void Pop()
        {
            if (m_ProcessStack.Count == 0)
            {
                Debug.LogError("Process Stack is empty");
                return;
            }

            int index = m_ProcessStack.Count - 1;
            m_ProcessStack[index].OnDeactivate();
            m_ProcessStack[index].OnUninitialize();
            Destroy(m_ProcessStack[index]);
            m_ProcessStack.RemoveAt(index);

            if (index > 0)
            {
                m_ProcessStack[index - 1].OnActivate();
            }
        }

        private void PopAll()
        {
            while (m_ProcessStack.Count > 0)
            {
                Pop();
            }
        }


    }
}