using SnowEater.Core.Events;
using SnowEater.Core.Managers.WindowManager;
using UnityEngine.UIElements;

[UxmlElement]
public partial class PauseScreen : Window
{
    private static readonly string RETURN_TO_GAME_BUTTON = "pause-screen__return-to-game-btn";

    VisualElement _returnToGameButton;

    public PauseScreen() { }

    ~PauseScreen() { }

    public override void ShowWindow()
    {
        base.ShowWindow();
        Initialize();
    }

    void Initialize()
    {
        _returnToGameButton = contentContainer.Q(RETURN_TO_GAME_BUTTON);
        if (_returnToGameButton != null)
        {
            // Ensure the element can be focused by navigation.
            _returnToGameButton.focusable = true;
            Focus(); // Focus to this window panel first. //TODO: Perhaps this needs to get integrated into base.ShowWindow()? Might cause issue's if there are multiple windows though.
            _returnToGameButton.Focus();
            // Register callbacks for both mouse and controller/keyboard input.
            _returnToGameButton.RegisterCallback<ClickEvent>(OnSubmit_ReturnToGame);
            _returnToGameButton.RegisterCallback<NavigationSubmitEvent>(OnSubmit_ReturnToGame);
        }
    }

    private void OnSubmit_ReturnToGame(EventBase evt)
    {
        EventManager.TriggerEvent(new PauseMenuEvent());
    }
}