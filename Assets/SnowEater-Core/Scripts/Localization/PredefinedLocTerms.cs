// Copyright Snow Eater Studios

using SnowEater.Core.Localization;
using Isto.Core.Localization;

namespace SnowEater.Core.Localization
{
    /// <summary>
    /// Enum representing all predefined LocTerm types available in the project.
    /// This allows UI Builder to show a dropdown of available LocTerms.
    /// </summary>
    public enum PredefinedLocTerm
    {
        None,
        CommonYes,
        CommonQuit,
        CommonStart,
        CommonContinue,
        // Add more as you create new LocTerm classes
    }

    /// <summary>
    /// Factory for creating predefined LocTerm instances.
    /// </summary>
    public static class PredefinedLocTermFactory
    {
        /// <summary>
        /// Creates a LocTerm instance based on the predefined type.
        /// </summary>
        /// <param name="termType">The predefined LocTerm type</param>
        /// <returns>A LocTerm instance, or null if None is selected</returns>
        public static LocTerm Create(PredefinedLocTerm termType)
        {
            return termType switch
            {
                PredefinedLocTerm.CommonYes => new CommonYesTerm(),
                PredefinedLocTerm.CommonQuit => new CommonQuitTerm(),
                PredefinedLocTerm.CommonStart => new CommonStartTerm(),
                PredefinedLocTerm.CommonContinue => new CommonContinueTerm(),
                PredefinedLocTerm.None => null,
                _ => null
            };
        }

        /// <summary>
        /// Gets the PredefinedLocTerm enum value for a given LocTerm instance.
        /// </summary>
        /// <param name="locTerm">The LocTerm instance</param>
        /// <returns>The corresponding enum value, or None if not found</returns>
        public static PredefinedLocTerm GetTermType(LocTerm locTerm)
        {
            return locTerm switch
            {
                CommonYesTerm => PredefinedLocTerm.CommonYes,
                CommonQuitTerm => PredefinedLocTerm.CommonQuit,
                CommonStartTerm => PredefinedLocTerm.CommonStart,
                CommonContinueTerm => PredefinedLocTerm.CommonContinue,
                null => PredefinedLocTerm.None,
                _ => PredefinedLocTerm.None
            };
        }
    }
}
