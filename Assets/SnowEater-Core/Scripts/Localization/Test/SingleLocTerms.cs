// Copyright Isto Inc.

using SnowEater.Core.Localization;

namespace Isto.Core.Localization
{
    public class CommonYesTerm : SnowEaterLocalizedString
    {
        public CommonYesTerm() : base("Common_Yes") { }
    }

    public class CommonQuitTerm : SnowEaterLocalizedString
    {
        public CommonQuitTerm() : base("Common_Quit") { }
    }

    public class CommonStartTerm : SnowEaterLocalizedString
    {
        public CommonStartTerm() : base("Common_Start") { }
    }

    public class CommonContinueTerm : SnowEaterLocalizedString
    {
        public CommonContinueTerm() : base("Common_Continue") { }
    }
}