using System;
using System.Collections.Generic;
using UnityEngine;

namespace SnowEater.Core.Events
{
    public delegate void EventHandler<T>(T evt) where T : class, IEvent;

    public static class EventManager
    {
        private static Dictionary<Type, Delegate> m_EventLookup = new Dictionary<Type, Delegate>();

        public static void Subscribe<T>(EventHandler<T> listener) where T : class, IEvent
        {
            var key = typeof(T);

            if (m_EventLookup.ContainsKey(key))
            {
                m_EventLookup[key] = (m_EventLookup[key] as EventHandler<T>) + listener;
            }
            else
            {
                m_EventLookup.Add(key, listener);
            }
        }

        public static void Unsubscribe<T>(EventHandler<T> listener) where T : class, IEvent
        {
            var key = typeof(T);

            if (m_EventLookup.ContainsKey(key))
            {
                m_EventLookup[key] = (m_EventLookup[key] as EventHandler<T>) - listener;
            }
        }

        public static void TriggerEvent<T>(T evt) where T : class, IEvent
        {
            if(!m_EventLookup.TryGetValue(evt.Type, out var _delegate) || _delegate == null)
                return;

            try
            {
                (_delegate as EventHandler<T>)?.Invoke(evt);
            }
            catch (Exception exception)
            {
                Debug.LogError(exception);
            }
        }
    }
}