using SnowEater.Core.Services;
using SnowEater.Core.StateMachine.GameState;
using System;

namespace SnowEater.Core.Events
{
    public class PauseMenuEvent : IEvent
    {
        public Type Type => typeof(PauseMenuEvent);
        private IStateMachineService _stateMachineService;

        public PauseMenuEvent()
        {
            _stateMachineService = Systems.Services.Get<IStateMachineService>();
            if (_stateMachineService.CurrentState.StateKey == GameStackableStateEnum.PAUSE_STATE)
            {
                _stateMachineService.ExitCurrentSubState();
            }
            else if (_stateMachineService.CurrentState.StateKey == GameStackableStateEnum.GAMEPLAY_STATE)
            {
                _stateMachineService.EnterGameSubState(GameStackableStateEnum.PAUSE_STATE);
            }
        }
    }
}