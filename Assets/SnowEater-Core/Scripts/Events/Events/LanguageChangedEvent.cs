// Copyright Snow Eater Studios

using SnowEater.Core.Localization;
using System;

namespace SnowEater.Core.Events
{
    public class LanguageChangedEvent : IEvent
    {
        public Type Type => typeof(LanguageChangedEvent);
        public LocalizationLanguageEnum NewLanguage { get; private set; }

        public LanguageChangedEvent(LocalizationLanguageEnum language)
        {
            NewLanguage = language;
        }
    }
}