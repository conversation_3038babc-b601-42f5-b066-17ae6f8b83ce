<ui:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/Styles/TemplateUSS.uss?fileID=7433441132597879392&amp;guid=eaa1ff4a10c3d497692244c7090d4c77&amp;type=3#TemplateUSS" />
    <SnowEater.Core.UI.Modals.YesNoModal name="YesNoModal" class="WindowHolder-Size" style="align-items: center;">
        <ui:VisualElement name="background" style="flex-grow: 0; width: 50%; height: 50%; background-color: rgb(70, 70, 70); flex-shrink: 0; top: 25%; border-top-right-radius: 0%; border-top-left-radius: 0%; border-bottom-left-radius: 5%; border-bottom-right-radius: 5%; justify-content: space-between; align-items: stretch;">
            <ui:Label text="Title" name="yes-no-modal__title" style="background-color: rgb(0, 0, 0); -unity-font-style: bold; font-size: 42px; color: rgb(255, 255, 255); -unity-text-align: upper-center;" />
            <ui:Label text="This is some sort of text that showcases how long some of this content can be. We need to ensure that we accomodate mucho text." name="yes-no-modal__content" style="flex-grow: 0; font-size: 32px; height: 75%; -unity-text-align: middle-center; white-space: normal; width: 95%; left: 2%; color: rgb(27, 27, 27); background-color: rgba(168, 155, 155, 0.44);" />
            <ui:VisualElement style="flex-grow: 1; flex-direction: row; justify-content: space-around; align-items: center;">
                <ui:Button text="Button" name="yes-no-modal__cancel-btn" class="GenericButton" />
                <ui:Button text="Button" name="yes-no-modal__confirm-btn" class="GenericButton" />
            </ui:VisualElement>
        </ui:VisualElement>
    </SnowEater.Core.UI.Modals.YesNoModal>
</ui:UXML>
