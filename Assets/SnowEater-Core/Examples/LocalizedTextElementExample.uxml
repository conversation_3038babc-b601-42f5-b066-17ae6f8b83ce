<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <ui:VisualElement style="flex-grow: 1; padding: 20px;">
        <ui:Label text="LocalizedTextElement Examples" style="font-size: 24px; margin-bottom: 20px; -unity-font-style: bold;" />
        
        <!-- Example 1: Using predefined LocTerm (dropdown selection in UI Builder) -->
        <ui:VisualElement style="margin-bottom: 15px;">
            <ui:Label text="Predefined LocTerm (CommonYes):" style="margin-bottom: 5px;" />
            <SnowEater.Core.UI.LocalizedTextElement predefined-loc-term="CommonYes" style="background-color: rgba(0, 255, 0, 0.1); padding: 5px;" />
        </ui:VisualElement>
        
        <!-- Example 2: Using predefined LocTerm (CommonQuit) -->
        <ui:VisualElement style="margin-bottom: 15px;">
            <ui:Label text="Predefined LocTerm (CommonQuit):" style="margin-bottom: 5px;" />
            <SnowEater.Core.UI.LocalizedTextElement predefined-loc-term="CommonQuit" style="background-color: rgba(255, 0, 0, 0.1); padding: 5px;" />
        </ui:VisualElement>
        
        <!-- Example 3: Using manual localization key (fallback) -->
        <ui:VisualElement style="margin-bottom: 15px;">
            <ui:Label text="Manual Localization Key:" style="margin-bottom: 5px;" />
            <SnowEater.Core.UI.LocalizedTextElement localization-key="Common_Start" style="background-color: rgba(0, 0, 255, 0.1); padding: 5px;" />
        </ui:VisualElement>
        
        <!-- Example 4: No selection (None) -->
        <ui:VisualElement style="margin-bottom: 15px;">
            <ui:Label text="No Selection (None):" style="margin-bottom: 5px;" />
            <SnowEater.Core.UI.LocalizedTextElement predefined-loc-term="None" style="background-color: rgba(128, 128, 128, 0.1); padding: 5px;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
