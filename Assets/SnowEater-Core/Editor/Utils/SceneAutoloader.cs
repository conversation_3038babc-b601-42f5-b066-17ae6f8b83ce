using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;

namespace SnowEater.Utils
{
    /// <summary>
    /// Scene auto loader.
    /// </summary>
    /// <description>
    /// This class adds a File > Scene Autoload menu containing options to select
    /// a "master scene" enable it to be auto-loaded when the user presses play
    /// in the editor. When enabled, the selected scene will be loaded on play,
    /// then the original scene will be reloaded on stop.
    ///
    /// Based on an idea on this thread:
    /// http://forum.unity3d.com/threads/157502-Executing-first-scene-in-build-settings-when-pressing-play-button-in-editor
    /// </description>
    [InitializeOnLoad]
    public static class SceneAutoLoader
    {
        // Static constructor binds a playmode-changed callback.
        // [InitializeOnLoad] above makes sure this gets executed.
        static SceneAutoLoader()
        {
            EditorApplication.playModeStateChanged += OnPlayModeChanged;
        }

        // Menu items to select the "master" scene and control whether or not to load it.
        [MenuItem("Snow Eater/Utils/Scene Autoload/Select Master Scene...")]
        private static void SelectMasterScene()
        {
            string masterScene = EditorUtility.OpenFilePanel("Select Master Scene", Application.dataPath, "unity");
            masterScene =
                masterScene.Replace(Application.dataPath, "Assets"); //project relative instead of absolute path
            if (!string.IsNullOrEmpty(masterScene))
            {
                MasterScene = masterScene;
                LoadMasterOnPlay = true;
            }
        }

        [MenuItem("Snow Eater/Utils/Scene Autoload/Load Master On Play", true)]
        private static bool ShowLoadMasterOnPlay()
        {
            return !LoadMasterOnPlay;
        }

        [MenuItem("Snow Eater/Utils/Scene Autoload/Load Master On Play")]
        private static void EnableLoadMasterOnPlay()
        {
            LoadMasterOnPlay = true;
        }

        [MenuItem("Snow Eater/Utils/Scene Autoload/Don't Load Master On Play", true)]
        private static bool ShowDontLoadMasterOnPlay()
        {
            return LoadMasterOnPlay;
        }

        [MenuItem("Snow Eater/Utils/Scene Autoload/ Don't Load Master On Play")]
        private static void DisableLoadMasterOnPlay()
        {
            LoadMasterOnPlay = false;
        }

        // Play mode change callback handles the scene load/reload.
        private static void OnPlayModeChanged(PlayModeStateChange state)
        {
            if (!LoadMasterOnPlay || IsInTestMode)
            {
                return;
            }

            if (!EditorApplication.isPlaying && EditorApplication.isPlayingOrWillChangePlaymode)
            {
                // User pressed play -- autoload master scene.
                PreviousScenes = BuildPreviousScenesList();
                PreviousScenesLoadedState = BuildPreviousScenesLoadedList();
                if (EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo())
                {
                    try
                    {
                        EditorSceneManager.OpenScene(MasterScene);
                    }
                    catch
                    {
                        Debug.LogError(string.Format("error: scene not found: {0}", MasterScene));
                        EditorApplication.isPlaying = false;
                    }
                }
                else
                {
                    // User cancelled the save operation -- cancel play as well.
                    EditorApplication.isPlaying = false;
                }
            }

            // isPlaying check required because cannot OpenScene while playing
            if (!EditorApplication.isPlaying && !EditorApplication.isPlayingOrWillChangePlaymode)
            {
                // User pressed stop -- reload previous scenes.
                foreach (var tuple in Enumerable.Zip(PreviousScenes, PreviousScenesLoadedState,
                             (scene, loadedState) => { return new Tuple<string, bool>(scene, loadedState); }))
                {
                    try
                    {
                        if (tuple.Item2)
                        {
                            EditorSceneManager.OpenScene(tuple.Item1, OpenSceneMode.Additive);
                        }
                        else
                        {
                            EditorSceneManager.OpenScene(tuple.Item1, OpenSceneMode.AdditiveWithoutLoading);
                        }
                    }
                    catch
                    {
                        Debug.LogError(string.Format("Error: scene not found: {0}", tuple.Item1));
                    }
                }
            }
        }

        private static string[] BuildPreviousScenesList()
        {
            var result = new List<string>();
            for (int i = 0; i < EditorSceneManager.sceneCount; i++)
            {
                var scene = EditorSceneManager.GetSceneAt(i);
                result.Add(scene.path);
            }

            return result.ToArray();
        }

        private static bool[] BuildPreviousScenesLoadedList()
        {
            var result = new List<bool>();
            for (int i = 0; i < EditorSceneManager.sceneCount; i++)
            {
                var scene = EditorSceneManager.GetSceneAt(i);
                result.Add(scene.isLoaded);
            }

            return result.ToArray();
        }

        // Properties are remembered as editor preferences.
        private const string cEditorPrefLoadMasterOnPlay = "SceneAutoLoader.LoadMasterOnPlay";

        private const string cEditorPrefMasterScene = "SceneAutoLoader.MasterScene";
        private const string cEditorPrefPreviousScenes = "SceneAutoLoader.PreviousScenes";
        private const string cEditorPrefPreviousScenesLoadedState = "SceneAutoLoader.PreviousScenesLoadedState";

        private static bool LoadMasterOnPlay
        {
            get { return EditorPrefs.GetBool(cEditorPrefLoadMasterOnPlay, false); }
            set { EditorPrefs.SetBool(cEditorPrefLoadMasterOnPlay, value); }
        }

        private static string MasterScene
        {
            get { return EditorPrefs.GetString(cEditorPrefMasterScene, "Master.unity"); }
            set { EditorPrefs.SetString(cEditorPrefMasterScene, value); }
        }

        private static string[] PreviousScenes
        {
            get
            {
                return EditorPrefs.GetString(cEditorPrefPreviousScenes, String.Join(";", BuildPreviousScenesList()))
                    .Split(';');
            }
            set { EditorPrefs.SetString(cEditorPrefPreviousScenes, String.Join(";", value)); }
        }

        private static bool[] PreviousScenesLoadedState
        {
            get
            {
                var strings = EditorPrefs.GetString(cEditorPrefPreviousScenesLoadedState,
                    String.Join(";", BuildPreviousScenesLoadedList())).Split(';');
                List<bool> result = new List<bool>(strings.Length);
                foreach (var s in strings)
                {
                    result.Add(bool.Parse(s));
                }

                return result.ToArray();
            }
            set { EditorPrefs.SetString(cEditorPrefPreviousScenesLoadedState, String.Join(";", value)); }
        }

        public static bool IsInTestMode
        {
            get { return EditorSceneManager.GetActiveScene().path.Contains("InitTestScene"); }
        }
    }
}