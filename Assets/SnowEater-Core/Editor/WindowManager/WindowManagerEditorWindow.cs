using SnowEater.Core.Managers.WindowManager;
using System.Collections.Generic;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

namespace SnowEater.Managers.Editor
{
	[CustomEditor(typeof(WindowManager))]
	public class WindowManagerEditorWindow : UnityEditor.Editor
	{
		private class LayerView : Foldout
		{
			private Foldout m_ActiveFoldout;
			private Foldout m_AvailableFoldout;

			public LayerView(LayerStates data, List<Window> windows, List<WindowHolder> windowHolders)
			{
				AddToClassList("LayerView");
				this.text = data.LayerName;
				this.value = false;
				m_ActiveFoldout = new Foldout();
				m_AvailableFoldout = new Foldout();

				data.ActiveContainer = m_ActiveFoldout;
				if (windows != null)
				{
					foreach (var window in windows)
					{
						var type = window.GetType();
						data.AddWindow(type);
					}
				}

				foreach (var windowHolder in windowHolders)
				{
					Button button = new Button(() =>
					{
						WindowManager.Instance.PushWindow(windowHolder.GetWindowType());
					}) { text = "Open", pickingMode = Application.isPlaying?PickingMode.Position:PickingMode.Ignore};
					button.AddToClassList(Application.isPlaying ? "ButtonActive" : "ButtonDisabled");
					VisualElement container = new VisualElement();
					container.AddToClassList("AvailableWindow");
					container.Add(new Label(windowHolder.GetWindowType().Name));
					container.Add(button);
					m_AvailableFoldout.Add(container);
				}

				Add(m_ActiveFoldout);
				Add(m_AvailableFoldout);

				m_ActiveFoldout.text = $"Active ({m_ActiveFoldout.childCount})";
				m_AvailableFoldout.text = $"Available ({m_AvailableFoldout.childCount})";
			}
		}

		private class LayerStates
		{
			public string LayerName;
			public Foldout ActiveContainer;
			public List<System.Tuple<System.Type, VisualElement>> ActiveWindows = new();

			public void GetAndRemoveWindow(System.Type windowType)
			{
				for (var i = 0; i < ActiveWindows.Count; i++)
				{
					var tuple = ActiveWindows[i];
					if (tuple.Item1 != windowType) continue;
					ActiveWindows.Remove(tuple);
					if(ActiveContainer.Contains(tuple.Item2))
						ActiveContainer.Remove(tuple.Item2);

					ActiveContainer.text = $"Active ({ActiveContainer.childCount})";
					ActiveContainer.value = ActiveContainer.childCount > 0;
					return;
				}
			}

			public void AddWindow(System.Type windowType)
			{
				foreach (var tuple in ActiveWindows)
				{
					if (tuple.Item1 == windowType) return;
				}

				var container = new VisualElement();
				container.Add(new Label(windowType.Name));
				container.Add(new Button(()=>WindowManager.Instance.PopWindow(LayerName)){text = "Pop Layer"});
				container.AddToClassList("ActiveWindow");
				ActiveContainer.Add(container);
				ActiveContainer.value = true;
				ActiveContainer.text = $"Active ({ActiveContainer.childCount})";
				ActiveWindows.Add(new System.Tuple<System.Type, VisualElement>(windowType, container));
			}
		}

		private LayerStates[] m_LayerStates;
		private StyleSheet m_StyleSheet;

		private void OnEnable()
		{
			m_StyleSheet =
				AssetDatabase.LoadAssetAtPath<StyleSheet>(
					"Assets/Editor/UI/Uss/WindowManagerEditor.uss");
			EditorApplication.playModeStateChanged += EditorApplicationOnplayModeStateChanged;
		}

		private void OnDisable()
		{
			m_StyleSheet = null;
			EditorApplication.playModeStateChanged -= EditorApplicationOnplayModeStateChanged;
			var manager = serializedObject.targetObject as WindowManager;
			if (manager == null)
			{
				return;
			}

			manager.OnWindowShowStarted -= ManagerOnOnWindowShowStarted;
			manager.OnWindowHideStarted -= ManagerOnOnWindowHideStarted;
		}

		private void EditorApplicationOnplayModeStateChanged(PlayModeStateChange obj)
		{
			var manager = serializedObject.targetObject as WindowManager;
			if (manager == null)
			{
				return;
			}

			if (obj == PlayModeStateChange.EnteredPlayMode)
			{
				manager.OnWindowShowStarted += ManagerOnOnWindowShowStarted;
				manager.OnWindowHideStarted += ManagerOnOnWindowHideStarted;

			}else if (obj == PlayModeStateChange.ExitingPlayMode)
			{
				manager.OnWindowShowStarted -= ManagerOnOnWindowShowStarted;
				manager.OnWindowHideStarted -= ManagerOnOnWindowHideStarted;
			}
		}

		private void ManagerOnOnWindowHideStarted(System.Type obj)
		{
			var holder = GetWindowData().GetWindowHolder(obj);
			var state = GetLayerState(holder.LayerName);
			if(state == null) return;
			state.GetAndRemoveWindow(obj);
			state.ActiveContainer.text = $"Active ({state.ActiveContainer.childCount})";
		}

		private void ManagerOnOnWindowShowStarted(System.Type obj)
		{
			var holder = GetWindowData().GetWindowHolder(obj);
			var state = GetLayerState(holder.LayerName);
			if(state == null) return;
			state.AddWindow(obj);
			state.ActiveContainer.text = $"Active ({state.ActiveContainer.childCount})";
		}

		private LayerStates GetLayerState(string holderLayerName)
		{
			if (m_LayerStates == null) return null;
			foreach (var layerState in m_LayerStates)
			{
				if (layerState.LayerName == holderLayerName) return layerState;
			}

			return null;
		}

		private WindowManagerData GetWindowData()
		{
			var dataProp = serializedObject.FindProperty("Data");
			WindowManagerData Data = null;
			if (dataProp != null)
			{
				Data = dataProp.objectReferenceValue as WindowManagerData;
			}

			return Data;
		}

		public override VisualElement CreateInspectorGUI()
		{
			VisualElement myInspector = new VisualElement();
			myInspector.styleSheets.Add(m_StyleSheet);
			InspectorElement.FillDefaultInspector(myInspector, serializedObject, this);

			var manager = serializedObject.targetObject as WindowManager;
			if (manager == null)
			{
				return myInspector;
			}

			var windowManagerData = GetWindowData();
			if (windowManagerData == null)
			{
				myInspector.Add(new Label("WindowManagerData not set!"));
				return myInspector;
			}

			var subTitle = new Label("Layers");
			subTitle.AddToClassList("SubTitle");
			myInspector.Add(subTitle);
			m_LayerStates = new LayerStates[manager.Layers.Length];

			for (var i = 0; i < manager.Layers.Length; i++)
			{
				// Prep Data
				var layer = manager.Layers[i];
				m_LayerStates[i] = new LayerStates() {LayerName = layer.LayerName};
				List<Window> windows = manager.IsReady ? manager.GetLayer(layer.LayerName).Windows : null;
				List<WindowHolder> layersWindows = new List<WindowHolder>();
				foreach (var windowHolder in windowManagerData.WindowHolders)
				{
					if (windowHolder.LayerName != layer.LayerName) continue;
					layersWindows.Add(windowHolder);
				}
				// Set Data to Render
				LayerView layerView =
					new LayerView(m_LayerStates[i], windows, layersWindows);
				myInspector.Add(layerView);
			}

			return myInspector;
		}
	}
}